"""Add visibility field to workout_templates

Revision ID: add_visibility_field
Revises: 
Create Date: 2024-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_visibility_field'
down_revision = None  # 请根据实际情况设置上一个revision
branch_labels = None
depends_on = None


def upgrade():
    # 添加visibility字段到workout_templates表
    op.add_column('workout_templates', sa.Column('visibility', sa.String(length=20), nullable=True))
    
    # 设置默认值为'private'
    op.execute("UPDATE workout_templates SET visibility = 'private' WHERE visibility IS NULL")
    
    # 可选：如果需要设置为非空字段，取消下面的注释
    # op.alter_column('workout_templates', 'visibility', nullable=False)


def downgrade():
    # 删除visibility字段
    op.drop_column('workout_templates', 'visibility')
