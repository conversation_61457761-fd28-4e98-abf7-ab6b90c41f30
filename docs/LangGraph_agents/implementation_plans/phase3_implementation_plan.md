# 阶段三：高级功能整合实施计划（第5-6周）

## 1. 阶段概览

### 1.1 目标和范围
- **主要目标**: 实现企业级错误处理和智能缓存系统
- **技术重点**: 多层错误处理、智能缓存、性能优化
- **时间周期**: 2周（14个工作日）
- **关键交付物**: 统一错误处理系统 + 智能缓存管理系统

### 1.2 当前系统分析
基于阶段一、二完成的核心整合，现有关键组件：

**已完成的核心组件**:
- `app/services/ai_assistant/integration/state_adapter.py` - 统一状态适配器
- `app/services/ai_assistant/integration/state_manager.py` - 整合状态管理器
- `app/services/ai_assistant/integration/intent_processor.py` - 整合意图处理器
- `app/services/ai_assistant/integration/parameter_manager.py` - 增强参数管理器
- `app/services/ai_assistant/integration/enhanced_langgraph_service.py` - 增强LangGraph服务

**需要增强的高级功能**:
- 错误处理和恢复机制
- 智能缓存策略
- 性能监控和优化
- 系统健康检查

**现有相关组件**:
- `app/services/ai_assistant/common/cache.py` - 基础缓存服务
- `app/services/ai_assistant/intelligence/monitoring/` - 监控相关模块
- 各模块中的错误处理逻辑

### 1.3 技术架构
```
app/services/ai_assistant/integration/
├── error_handler.py              # 新建：统一错误处理器
├── cache_manager.py              # 新建：智能缓存管理器
├── performance_monitor.py        # 新建：性能监控器
└── circuit_breaker.py            # 新建：熔断器实现
```

## 2. 第5周：错误处理系统整合

### 2.1 Day 29-31: 统一错误处理器

#### 2.1.1 文件创建清单
- [ ] `app/services/ai_assistant/integration/error_handler.py`
- [ ] `app/services/ai_assistant/integration/circuit_breaker.py`
- [ ] `app/services/ai_assistant/integration/retry_manager.py`
- [ ] `tests/integration/test_error_handler.py`

#### 2.1.2 核心实现 - error_handler.py
```python
import asyncio
import traceback
from enum import Enum
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass

from ...logger.logger import get_logger

logger = get_logger(__name__)

class ErrorSeverity(Enum):
    """错误严重级别"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    """错误分类"""
    SYSTEM = "system"           # 系统错误
    LLM = "llm"                # LLM服务错误
    DATABASE = "database"       # 数据库错误
    NETWORK = "network"         # 网络错误
    VALIDATION = "validation"   # 参数验证错误
    BUSINESS = "business"       # 业务逻辑错误
    TIMEOUT = "timeout"         # 超时错误

@dataclass
class ErrorInfo:
    """错误信息"""
    error_id: str
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    details: Dict[str, Any]
    timestamp: datetime
    conversation_id: str
    user_id: str
    stack_trace: str
    context: Dict[str, Any]

class UnifiedErrorHandler:
    """统一错误处理器 - 企业级错误处理体系"""
    
    def __init__(self, db_session, redis_client):
        self.db = db_session
        self.redis = redis_client
        
        # 错误处理策略映射
        self.error_strategies = self._init_error_strategies()
        
        # 错误统计
        self.error_stats = {}
        
        # 熔断器配置
        self.circuit_breakers = {}
        
        # 重试配置
        self.retry_config = {
            ErrorCategory.LLM: {"max_retries": 3, "backoff_factor": 2.0},
            ErrorCategory.DATABASE: {"max_retries": 2, "backoff_factor": 1.5},
            ErrorCategory.NETWORK: {"max_retries": 3, "backoff_factor": 2.0},
        }
    
    def _init_error_strategies(self) -> Dict[ErrorCategory, Dict[ErrorSeverity, Callable]]:
        """初始化错误处理策略"""
        return {
            ErrorCategory.LLM: {
                ErrorSeverity.LOW: self._handle_llm_low_severity,
                ErrorSeverity.MEDIUM: self._handle_llm_medium_severity,
                ErrorSeverity.HIGH: self._handle_llm_high_severity,
                ErrorSeverity.CRITICAL: self._handle_llm_critical_severity,
            },
            ErrorCategory.DATABASE: {
                ErrorSeverity.LOW: self._handle_db_low_severity,
                ErrorSeverity.MEDIUM: self._handle_db_medium_severity,
                ErrorSeverity.HIGH: self._handle_db_high_severity,
                ErrorSeverity.CRITICAL: self._handle_db_critical_severity,
            },
            ErrorCategory.NETWORK: {
                ErrorSeverity.LOW: self._handle_network_low_severity,
                ErrorSeverity.MEDIUM: self._handle_network_medium_severity,
                ErrorSeverity.HIGH: self._handle_network_high_severity,
                ErrorSeverity.CRITICAL: self._handle_network_critical_severity,
            },
            ErrorCategory.VALIDATION: {
                ErrorSeverity.LOW: self._handle_validation_error,
                ErrorSeverity.MEDIUM: self._handle_validation_error,
                ErrorSeverity.HIGH: self._handle_validation_error,
                ErrorSeverity.CRITICAL: self._handle_validation_error,
            },
            ErrorCategory.BUSINESS: {
                ErrorSeverity.LOW: self._handle_business_low_severity,
                ErrorSeverity.MEDIUM: self._handle_business_medium_severity,
                ErrorSeverity.HIGH: self._handle_business_high_severity,
                ErrorSeverity.CRITICAL: self._handle_business_critical_severity,
            },
            ErrorCategory.TIMEOUT: {
                ErrorSeverity.LOW: self._handle_timeout_error,
                ErrorSeverity.MEDIUM: self._handle_timeout_error,
                ErrorSeverity.HIGH: self._handle_timeout_error,
                ErrorSeverity.CRITICAL: self._handle_timeout_error,
            }
        }
    
    async def handle_error(self, 
                          error: Exception,
                          context: Dict[str, Any],
                          conversation_id: str = "",
                          user_id: str = "") -> Dict[str, Any]:
        """统一错误处理入口"""
        try:
            # 1. 分析错误类型和严重性
            error_info = await self._analyze_error(error, context, conversation_id, user_id)
            
            # 2. 记录错误
            await self._log_error(error_info)
            
            # 3. 更新错误统计
            await self._update_error_stats(error_info)
            
            # 4. 检查熔断器状态
            circuit_breaker = await self._get_circuit_breaker(error_info.category)
            if circuit_breaker and circuit_breaker.is_open():
                return await self._handle_circuit_open(error_info)
            
            # 5. 执行错误处理策略
            strategy = self.error_strategies.get(error_info.category, {}).get(error_info.severity)
            if strategy:
                result = await strategy(error_info)
            else:
                result = await self._handle_default_error(error_info)
            
            # 6. 更新熔断器状态
            if circuit_breaker:
                if result.get("success", False):
                    circuit_breaker.record_success()
                else:
                    circuit_breaker.record_failure()
            
            return result
            
        except Exception as e:
            logger.critical(f"错误处理器自身出现错误: {str(e)}")
            return {
                "success": False,
                "error_message": "系统遇到严重错误，请联系技术支持",
                "should_retry": False,
                "recovery_action": "escalate_to_human"
            }
    
    async def _analyze_error(self, 
                           error: Exception,
                           context: Dict[str, Any],
                           conversation_id: str,
                           user_id: str) -> ErrorInfo:
        """分析错误类型和严重性"""
        
        error_message = str(error)
        error_type = type(error).__name__
        
        # 错误分类
        category = self._classify_error(error, error_message)
        
        # 严重性评估
        severity = self._assess_severity(error, category, context)
        
        # 生成错误ID
        error_id = f"{category.value}_{int(datetime.now().timestamp())}_{conversation_id[:8]}"
        
        return ErrorInfo(
            error_id=error_id,
            category=category,
            severity=severity,
            message=error_message,
            details={
                "error_type": error_type,
                "error_args": getattr(error, 'args', []),
                "context_summary": self._summarize_context(context)
            },
            timestamp=datetime.now(),
            conversation_id=conversation_id,
            user_id=user_id,
            stack_trace=traceback.format_exc(),
            context=context
        )
    
    def _classify_error(self, error: Exception, error_message: str) -> ErrorCategory:
        """错误分类"""
        error_type = type(error).__name__
        
        # LLM相关错误
        if any(keyword in error_message.lower() for keyword in 
               ["api", "token", "rate limit", "quota", "model", "llm"]):
            return ErrorCategory.LLM
        
        # 数据库相关错误
        if any(keyword in error_type.lower() for keyword in
               ["sql", "database", "connection", "transaction"]):
            return ErrorCategory.DATABASE
        
        # 网络相关错误
        if any(keyword in error_type.lower() for keyword in
               ["connection", "timeout", "network", "request", "response"]):
            return ErrorCategory.NETWORK
        
        # 验证相关错误
        if any(keyword in error_type.lower() for keyword in
               ["validation", "value", "type", "format"]):
            return ErrorCategory.VALIDATION
        
        # 超时错误
        if "timeout" in error_message.lower():
            return ErrorCategory.TIMEOUT
        
        # 默认为业务逻辑错误
        return ErrorCategory.BUSINESS
    
    def _assess_severity(self, 
                        error: Exception,
                        category: ErrorCategory,
                        context: Dict[str, Any]) -> ErrorSeverity:
        """评估错误严重性"""
        
        error_message = str(error).lower()
        
        # 关键词映射严重性
        critical_keywords = ["critical", "fatal", "corruption", "security"]
        high_keywords = ["failed", "error", "exception", "unable"]
        medium_keywords = ["warning", "retry", "timeout"]
        
        if any(keyword in error_message for keyword in critical_keywords):
            return ErrorSeverity.CRITICAL
        elif any(keyword in error_message for keyword in high_keywords):
            return ErrorSeverity.HIGH
        elif any(keyword in error_message for keyword in medium_keywords):
            return ErrorSeverity.MEDIUM
        else:
            return ErrorSeverity.LOW
    
    # 错误处理策略实现
    async def _handle_llm_low_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理LLM低严重性错误"""
        return {
            "success": False,
            "error_message": "AI服务暂时不稳定，请稍后重试",
            "should_retry": True,
            "retry_delay": 5,
            "recovery_action": "use_fallback_response"
        }
    
    async def _handle_llm_high_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理LLM高严重性错误"""
        return {
            "success": False,
            "error_message": "AI服务遇到问题，已转人工处理",
            "should_retry": False,
            "recovery_action": "escalate_to_human"
        }
    
    async def _handle_db_critical_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理数据库严重错误"""
        # 通知运维团队
        await self._notify_ops_team(error_info)
        
        return {
            "success": False,
            "error_message": "系统维护中，请稍后重试",
            "should_retry": True,
            "retry_delay": 60,
            "recovery_action": "system_maintenance_mode"
        }
```

#### 2.1.3 验收标准
- [ ] 错误分类准确率 >95%
- [ ] 错误处理响应时间 <100ms
- [ ] 熔断器功能正常
- [ ] 错误恢复成功率 >80%

### 2.2 Day 32-35: 错误处理测试和优化

## 3. 第6周：智能缓存系统整合

### 3.1 Day 36-38: 智能缓存管理器

#### 3.1.1 文件创建清单
- [ ] `app/services/ai_assistant/integration/cache_manager.py`
- [ ] `app/services/ai_assistant/integration/cache_strategies.py`
- [ ] `tests/integration/test_cache_manager.py`

#### 3.1.2 核心实现 - cache_manager.py
```python
import asyncio
import json
import hashlib
from enum import Enum
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timedelta

from ...logger.logger import get_logger

logger = get_logger(__name__)

class CacheStrategy(Enum):
    """缓存策略"""
    LRU = "lru"                    # 最近最少使用
    TTL = "ttl"                    # 生存时间
    HYBRID = "hybrid"              # 混合策略
    INTELLIGENT = "intelligent"    # 智能策略

class CacheLevel(Enum):
    """缓存层级"""
    L1_MEMORY = "l1_memory"        # L1内存缓存
    L2_REDIS = "l2_redis"          # L2 Redis缓存
    L3_DATABASE = "l3_database"    # L3数据库缓存

class IntelligentCacheManager:
    """智能缓存管理器 - 多层智能缓存系统"""
    
    def __init__(self, redis_client, db_session):
        self.redis = redis_client
        self.db = db_session
        
        # L1内存缓存
        self.l1_cache = {}
        self.l1_access_times = {}
        self.l1_max_size = 1000
        
        # 缓存配置
        self.cache_config = {
            "user_profile": {
                "strategy": CacheStrategy.HYBRID,
                "ttl": 3600,  # 1小时
                "levels": [CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS]
            },
            "training_params": {
                "strategy": CacheStrategy.TTL,
                "ttl": 1800,  # 30分钟
                "levels": [CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS]
            },
            "conversation_state": {
                "strategy": CacheStrategy.INTELLIGENT,
                "ttl": 7200,  # 2小时
                "levels": [CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS, CacheLevel.L3_DATABASE]
            },
            "llm_responses": {
                "strategy": CacheStrategy.LRU,
                "ttl": 86400,  # 24小时
                "levels": [CacheLevel.L2_REDIS]
            },
            "exercise_data": {
                "strategy": CacheStrategy.HYBRID,
                "ttl": 86400,  # 24小时
                "levels": [CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS]
            }
        }
        
        # 智能缓存统计
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "errors": 0
        }
    
    async def get(self, key: str, cache_type: str = "default") -> Optional[Any]:
        """智能缓存获取"""
        try:
            config = self.cache_config.get(cache_type, self.cache_config["conversation_state"])
            
            # 按层级顺序查找
            for level in config["levels"]:
                if level == CacheLevel.L1_MEMORY:
                    result = await self._get_from_l1(key)
                elif level == CacheLevel.L2_REDIS:
                    result = await self._get_from_l2(key)
                elif level == CacheLevel.L3_DATABASE:
                    result = await self._get_from_l3(key, cache_type)
                
                if result is not None:
                    # 缓存命中，更新统计
                    self.cache_stats["hits"] += 1
                    
                    # 回填到更高层级缓存
                    await self._backfill_cache(key, result, level, config)
                    
                    logger.debug(f"缓存命中: {key} from {level.value}")
                    return result
            
            # 缓存未命中
            self.cache_stats["misses"] += 1
            logger.debug(f"缓存未命中: {key}")
            return None
            
        except Exception as e:
            self.cache_stats["errors"] += 1
            logger.error(f"缓存获取失败: {key}, 错误: {str(e)}")
            return None
    
    async def set(self, 
                  key: str, 
                  value: Any, 
                  cache_type: str = "default",
                  ttl: Optional[int] = None) -> bool:
        """智能缓存设置"""
        try:
            config = self.cache_config.get(cache_type, self.cache_config["conversation_state"])
            effective_ttl = ttl or config["ttl"]
            
            # 根据策略决定缓存层级
            strategy = config["strategy"]
            
            if strategy == CacheStrategy.INTELLIGENT:
                # 智能策略：根据访问模式决定缓存层级
                cache_levels = await self._intelligent_cache_decision(key, value, config)
            else:
                # 使用配置的缓存层级
                cache_levels = config["levels"]
            
            # 写入所有指定的缓存层级
            success_count = 0
            for level in cache_levels:
                if level == CacheLevel.L1_MEMORY:
                    if await self._set_to_l1(key, value, effective_ttl):
                        success_count += 1
                elif level == CacheLevel.L2_REDIS:
                    if await self._set_to_l2(key, value, effective_ttl):
                        success_count += 1
                elif level == CacheLevel.L3_DATABASE:
                    if await self._set_to_l3(key, value, cache_type, effective_ttl):
                        success_count += 1
            
            logger.debug(f"缓存设置: {key}, 成功层级数: {success_count}/{len(cache_levels)}")
            return success_count > 0
            
        except Exception as e:
            self.cache_stats["errors"] += 1
            logger.error(f"缓存设置失败: {key}, 错误: {str(e)}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            success_count = 0
            
            # 从所有层级删除
            if await self._delete_from_l1(key):
                success_count += 1
            if await self._delete_from_l2(key):
                success_count += 1
            if await self._delete_from_l3(key):
                success_count += 1
            
            logger.debug(f"缓存删除: {key}, 成功层级数: {success_count}")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"缓存删除失败: {key}, 错误: {str(e)}")
            return False
    
    async def _get_from_l1(self, key: str) -> Optional[Any]:
        """从L1内存缓存获取"""
        if key in self.l1_cache:
            self.l1_access_times[key] = datetime.now()
            return self.l1_cache[key]
        return None
    
    async def _set_to_l1(self, key: str, value: Any, ttl: int) -> bool:
        """设置到L1内存缓存"""
        try:
            # 检查缓存大小限制
            if len(self.l1_cache) >= self.l1_max_size:
                await self._evict_l1_cache()
            
            self.l1_cache[key] = value
            self.l1_access_times[key] = datetime.now()
            
            # 设置TTL（简化实现，实际应该使用定时器）
            # TODO: 实现精确的TTL机制
            
            return True
        except Exception as e:
            logger.error(f"L1缓存设置失败: {str(e)}")
            return False
    
    async def _evict_l1_cache(self):
        """L1缓存淘汰"""
        try:
            # LRU淘汰策略
            if self.l1_access_times:
                oldest_key = min(self.l1_access_times.keys(), 
                               key=lambda k: self.l1_access_times[k])
                del self.l1_cache[oldest_key]
                del self.l1_access_times[oldest_key]
                self.cache_stats["evictions"] += 1
                logger.debug(f"L1缓存淘汰: {oldest_key}")
        except Exception as e:
            logger.error(f"L1缓存淘汰失败: {str(e)}")
    
    async def _get_from_l2(self, key: str) -> Optional[Any]:
        """从L2 Redis缓存获取"""
        try:
            cached_data = await self.redis.get(f"cache:{key}")
            if cached_data:
                return json.loads(cached_data)
            return None
        except Exception as e:
            logger.error(f"L2缓存获取失败: {str(e)}")
            return None
    
    async def _set_to_l2(self, key: str, value: Any, ttl: int) -> bool:
        """设置到L2 Redis缓存"""
        try:
            serialized_value = json.dumps(value, default=str)
            await self.redis.setex(f"cache:{key}", ttl, serialized_value)
            return True
        except Exception as e:
            logger.error(f"L2缓存设置失败: {str(e)}")
            return False
    
    async def _intelligent_cache_decision(self, 
                                        key: str, 
                                        value: Any, 
                                        config: Dict) -> List[CacheLevel]:
        """智能缓存决策"""
        # 基于访问模式、数据大小、重要性等因素决定缓存层级
        
        # 获取访问频率
        access_frequency = await self._get_access_frequency(key)
        
        # 计算数据大小
        data_size = len(json.dumps(value, default=str))
        
        # 决策逻辑
        cache_levels = []
        
        # 高频访问且小数据：L1 + L2
        if access_frequency > 10 and data_size < 10240:  # 10KB
            cache_levels = [CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS]
        # 中频访问：L2
        elif access_frequency > 2:
            cache_levels = [CacheLevel.L2_REDIS]
        # 低频访问但重要数据：L2 + L3
        elif self._is_important_data(key):
            cache_levels = [CacheLevel.L2_REDIS, CacheLevel.L3_DATABASE]
        # 默认：L2
        else:
            cache_levels = [CacheLevel.L2_REDIS]
        
        logger.debug(f"智能缓存决策: {key}, 频率: {access_frequency}, 大小: {data_size}, 层级: {cache_levels}")
        return cache_levels
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.cache_stats["hits"] + self.cache_stats["misses"]
        hit_rate = self.cache_stats["hits"] / total_requests if total_requests > 0 else 0
        
        return {
            "hit_rate": hit_rate,
            "total_hits": self.cache_stats["hits"],
            "total_misses": self.cache_stats["misses"],
            "total_evictions": self.cache_stats["evictions"],
            "total_errors": self.cache_stats["errors"],
            "l1_cache_size": len(self.l1_cache),
            "l1_max_size": self.l1_max_size
        }
```

#### 3.1.3 验收标准
- [ ] 缓存命中率 >85%
- [ ] 缓存响应时间 <10ms
- [ ] 多层缓存一致性正确
- [ ] 智能缓存策略有效

### 3.2 Day 39-42: 性能优化和监控

## 4. 阶段三总结和交付

### 4.1 关键交付物
1. **统一错误处理系统** - 企业级错误处理体系
2. **智能缓存管理系统** - 多层智能缓存机制
3. **性能监控系统** - 全面的性能指标监控
4. **熔断器和重试机制** - 系统稳定性保障
5. **完整测试套件** - 错误处理和缓存系统测试

### 4.2 最终验收标准

#### 4.2.1 功能验收
- [ ] 错误处理系统全面覆盖
- [ ] 缓存系统智能高效
- [ ] 性能监控实时准确
- [ ] 熔断器和重试机制正常

#### 4.2.2 性能验收
- [ ] 错误处理响应时间 <100ms
- [ ] 缓存命中率 >85%
- [ ] 系统稳定性 >99.9%
- [ ] 错误恢复成功率 >80%

#### 4.2.3 质量验收
- [ ] 代码覆盖率 >90%
- [ ] 错误处理测试通过
- [ ] 缓存性能测试通过
- [ ] 系统压力测试通过

### 4.3 下一阶段准备
- [ ] 阶段四环境准备
- [ ] 生产部署环境配置
- [ ] 监控系统集成
- [ ] 上线前最终检查清单 